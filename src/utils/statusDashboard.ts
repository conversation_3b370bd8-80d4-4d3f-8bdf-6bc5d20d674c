import chalk from 'chalk';
import { ethers } from 'ethers';
import { splitScreenDashboard } from './splitScreenDashboard';

export interface SuccessfulTransaction {
  timestamp: number;
  type: 'flashloan' | 'arbitrage' | 'sandwich' | 'mev-share' | 'uniswap-v3-flash' | 'dynamic-flashloan';
  profit: bigint;
  gasUsed: bigint;
  txHash?: string;
  bundleHash?: string;
  confidence: number;
  details?: string;
}

export interface BotConfiguration {
  // Token configuration
  primaryToken: string;
  targetTokens: string[];
  enableAllTokenPairs: boolean;

  // DEX configuration
  flashloanDexPairs: string[];
  buyDex: string;
  sellDex: string;
  enableCrossDex: boolean;

  // Profit thresholds
  minProfitWei: string;
  minArbitrageSpread: string;
  minBackrunProfitEth: string;

  // Risk management
  maxPositionSizeEth: string;
  maxGasCostEth: string;
  slippageTolerance: string;

  // Scanning intervals
  arbitrageScanInterval: string;
  flashloanScanInterval: string;

  // Network settings
  chainId: number;
  dryRun: boolean;
}

export interface DashboardStats {
  // Mempool stats
  totalTransactions: number;
  relevantTransactions: number;
  transactionsPerMinute: number;

  // MEV opportunities
  opportunitiesFound: number;
  opportunitiesExecuted: number;
  totalProfit: bigint;

  // Gas tracking
  avgGasPrice: bigint;
  maxGasPrice: bigint;

  // Network status
  currentBlock: number;
  networkName: string;

  // Bot status
  isRunning: boolean;
  uptime: number;
  lastActivity: number;

  // Strategy status
  flashloanEnabled: boolean;
  mevShareEnabled: boolean;
  arbitrageEnabled: boolean;

  // Configuration
  configuration: BotConfiguration;

  // Error tracking
  errors: number;
  lastError?: string;

  // Successful transactions
  successfulTransactions: SuccessfulTransaction[];
}

/**
 * Live Status Dashboard
 * Provides a clean, updating status summary instead of spammy transaction logs
 */
export class StatusDashboard {
  private stats: DashboardStats;
  private startTime: number;
  private lastUpdate: number;
  private updateInterval: NodeJS.Timeout | null = null;
  private transactionBuffer: number[] = []; // Timestamps of recent transactions
  private successfulTxBuffer: SuccessfulTransaction[] = []; // Recent successful transactions
  private readonly BUFFER_SIZE = 100;
  private readonly SUCCESS_TX_BUFFER_SIZE = 20; // Keep last 20 successful transactions
  private readonly UPDATE_INTERVAL_MS = 5000; // Update every 5 seconds

  constructor() {
    this.startTime = Date.now();
    this.lastUpdate = Date.now();
    this.stats = {
      totalTransactions: 0,
      relevantTransactions: 0,
      transactionsPerMinute: 0,
      opportunitiesFound: 0,
      opportunitiesExecuted: 0,
      totalProfit: BigInt(0),
      avgGasPrice: BigInt(0),
      maxGasPrice: BigInt(0),
      currentBlock: 0,
      networkName: 'Unknown',
      isRunning: false,
      uptime: 0,
      lastActivity: Date.now(),
      flashloanEnabled: false,
      mevShareEnabled: false,
      arbitrageEnabled: false,
      configuration: {
        primaryToken: 'Not Set',
        targetTokens: [],
        enableAllTokenPairs: false,
        flashloanDexPairs: [],
        buyDex: 'Not Set',
        sellDex: 'Not Set',
        enableCrossDex: false,
        minProfitWei: '0',
        minArbitrageSpread: '0',
        minBackrunProfitEth: '0',
        maxPositionSizeEth: '0',
        maxGasCostEth: '0',
        slippageTolerance: '0',
        arbitrageScanInterval: '0',
        flashloanScanInterval: '0',
        chainId: 0,
        dryRun: false
      },
      errors: 0,
      successfulTransactions: []
    };
  }

  /**
   * Start the live dashboard updates (event-driven)
   */
  start(): void {
    if (this.updateInterval) {
      return;
    }

    this.stats.isRunning = true;

    // Check if we should use split screen dashboard
    if (process.env.SPLIT_SCREEN_DASHBOARD === 'true' || process.env.NODE_ENV === 'development') {
      this.startSplitScreenDashboard();
      return;
    }

    // Use event-driven updates with minimal periodic refresh for uptime
    this.updateInterval = setInterval(() => {
      this.updateStats();
      this.displayDashboard();
    }, 30000); // Reduced to 30 seconds for uptime updates only

    // Initial display
    this.displayDashboard();
  }

  /**
   * Start split screen dashboard (event-driven)
   */
  private startSplitScreenDashboard(): void {
    // Use event-driven updates with minimal periodic refresh for uptime
    this.updateInterval = setInterval(() => {
      this.updateStats();
      this.updateSplitScreenDashboard();
    }, 30000); // Reduced to 30 seconds for uptime updates only

    // Initial update and start (only if not already running)
    this.updateSplitScreenDashboard();
    if (!splitScreenDashboard.isActive()) {
      splitScreenDashboard.start();
    }
  }

  /**
   * Trigger immediate dashboard update (called by events)
   */
  triggerUpdate(): void {
    if (!this.stats.isRunning) {
      return;
    }

    this.updateStats();

    if (process.env.SPLIT_SCREEN_DASHBOARD === 'true' || process.env.NODE_ENV === 'development') {
      this.updateSplitScreenDashboard();
    } else {
      this.displayDashboard();
    }
  }

  /**
   * Update split screen dashboard with current data
   */
  private updateSplitScreenDashboard(): void {
    const dashboardData = {
      currentBlock: this.stats.currentBlock,
      networkName: this.stats.networkName,
      isRunning: this.stats.isRunning,
      uptime: Date.now() - this.stats.uptime,
      lastActivity: this.stats.lastActivity,
      flashloanEnabled: this.stats.flashloanEnabled,
      mevShareEnabled: this.stats.mevShareEnabled,
      arbitrageEnabled: this.stats.arbitrageEnabled,
      totalTransactions: this.stats.totalTransactions,
      relevantTransactions: this.stats.relevantTransactions,
      opportunitiesFound: this.stats.opportunitiesFound,
      opportunitiesExecuted: this.stats.opportunitiesExecuted,
      totalProfit: this.stats.totalProfit,
      avgGasPrice: this.stats.avgGasPrice,
      configuration: {
        tokenPairs: this.stats.configuration.targetTokens || [],
        dexes: this.stats.configuration.flashloanDexPairs || [],
        minProfitThreshold: this.stats.configuration.minProfitWei || '0',
        maxGasPrice: this.stats.configuration.maxGasCostEth || '0'
      },
      successfulTransactions: this.stats.successfulTransactions,
      errors: this.stats.errors,
      lastError: this.stats.lastError
    };

    splitScreenDashboard.updateDashboardData(dashboardData);
  }

  /**
   * Stop the dashboard updates
   */
  stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.stats.isRunning = false;

    // Stop split screen dashboard if running
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.stop();
    }
  }

  /**
   * Record a new transaction (event-driven update)
   */
  recordTransaction(isRelevant: boolean = false, gasPrice?: bigint): void {
    this.stats.totalTransactions++;
    if (isRelevant) {
      this.stats.relevantTransactions++;
    }

    // Add to buffer for rate calculation
    const now = Date.now();
    this.transactionBuffer.push(now);

    // Keep buffer size manageable
    if (this.transactionBuffer.length > this.BUFFER_SIZE) {
      this.transactionBuffer = this.transactionBuffer.slice(-this.BUFFER_SIZE);
    }

    // Update gas price stats
    if (gasPrice) {
      if (gasPrice > this.stats.maxGasPrice) {
        this.stats.maxGasPrice = gasPrice;
      }

      // Simple moving average for gas price
      if (this.stats.avgGasPrice === BigInt(0)) {
        this.stats.avgGasPrice = gasPrice;
      } else {
        this.stats.avgGasPrice = (this.stats.avgGasPrice + gasPrice) / BigInt(2);
      }
    }

    this.stats.lastActivity = now;

    // Trigger immediate update for relevant transactions
    if (isRelevant) {
      this.triggerUpdate();
    }
  }

  /**
   * Record an MEV opportunity (event-driven update)
   */
  recordOpportunity(executed: boolean = false, profit?: bigint): void {
    this.stats.opportunitiesFound++;
    if (executed) {
      this.stats.opportunitiesExecuted++;
      if (profit) {
        this.stats.totalProfit += profit;
      }
    }
    this.stats.lastActivity = Date.now();

    // Trigger immediate update for opportunities
    this.triggerUpdate();
  }

  /**
   * Record a successful transaction (event-driven update)
   */
  recordSuccessfulTransaction(transaction: SuccessfulTransaction): void {
    this.successfulTxBuffer.push(transaction);

    // Keep buffer size manageable
    if (this.successfulTxBuffer.length > this.SUCCESS_TX_BUFFER_SIZE) {
      this.successfulTxBuffer = this.successfulTxBuffer.slice(-this.SUCCESS_TX_BUFFER_SIZE);
    }

    // Update stats
    this.stats.opportunitiesExecuted++;
    this.stats.totalProfit += transaction.profit;
    this.stats.successfulTransactions = [...this.successfulTxBuffer];
    this.stats.lastActivity = Date.now();

    // Trigger immediate update for successful transactions
    this.triggerUpdate();
  }

  /**
   * Record an error
   */
  recordError(error: string): void {
    this.stats.errors++;
    this.stats.lastError = error;
    this.stats.lastActivity = Date.now();
  }

  /**
   * Update network status
   */
  updateNetworkStatus(blockNumber: number, networkName: string): void {
    this.stats.currentBlock = blockNumber;
    this.stats.networkName = networkName;
  }

  /**
   * Update block information (for event-driven updates)
   */
  updateBlockInfo(blockNumber: number, gasUsed: bigint, baseFeePerGas?: bigint): void {
    this.stats.currentBlock = blockNumber;

    // Update gas statistics if provided
    if (baseFeePerGas) {
      if (baseFeePerGas > this.stats.maxGasPrice) {
        this.stats.maxGasPrice = baseFeePerGas;
      }

      // Update average gas price
      if (this.stats.avgGasPrice === BigInt(0)) {
        this.stats.avgGasPrice = baseFeePerGas;
      } else {
        this.stats.avgGasPrice = (this.stats.avgGasPrice + baseFeePerGas) / BigInt(2);
      }
    }

    this.stats.lastActivity = Date.now();

    // Trigger immediate update for new blocks
    this.triggerUpdate();
  }

  /**
   * Update strategy status
   */
  updateStrategyStatus(flashloan: boolean, mevShare: boolean, arbitrage: boolean): void {
    this.stats.flashloanEnabled = flashloan;
    this.stats.mevShareEnabled = mevShare;
    this.stats.arbitrageEnabled = arbitrage;
  }

  /**
   * Update bot configuration
   */
  updateConfiguration(config: BotConfiguration): void {
    this.stats.configuration = { ...config };
  }

  /**
   * Update calculated stats
   */
  private updateStats(): void {
    const now = Date.now();
    this.stats.uptime = now - this.startTime;

    // Calculate transactions per minute
    const oneMinuteAgo = now - 60000;
    const recentTransactions = this.transactionBuffer.filter(timestamp => timestamp > oneMinuteAgo);
    this.stats.transactionsPerMinute = recentTransactions.length;
  }

  /**
   * Display the live dashboard
   */
  private displayDashboard(): void {
    // Don't display traditional dashboard if split screen is active
    if (splitScreenDashboard.isActive()) {
      return;
    }

    // Also check if SPLIT_SCREEN_DASHBOARD environment variable is set
    if (process.env.SPLIT_SCREEN_DASHBOARD === 'true') {
      return;
    }

    // Clear screen and move cursor to top
    process.stdout.write('\x1b[2J\x1b[H');

    const now = new Date();
    const uptime = this.formatUptime(this.stats.uptime);
    const lastActivity = this.formatTimeSince(this.stats.lastActivity);

    console.log(chalk.blue.bold('═══════════════════════════════════════════════════════════════'));
    console.log(chalk.blue.bold('                    🤖 MEV BOT LIVE STATUS DASHBOARD'));
    console.log(chalk.blue.bold('═══════════════════════════════════════════════════════════════'));
    console.log();

    // Status Overview
    console.log(chalk.yellow.bold('📊 STATUS OVERVIEW'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const statusColor = this.stats.isRunning ? chalk.green : chalk.red;
    const statusText = this.stats.isRunning ? 'RUNNING' : 'STOPPED';
    
    console.log(`${chalk.cyan('Status:')}          ${statusColor(statusText)}`);
    console.log(`${chalk.cyan('Network:')}         ${chalk.white(this.stats.networkName)} (Block: ${chalk.yellow(this.stats.currentBlock)})`);
    console.log(`${chalk.cyan('Uptime:')}          ${chalk.white(uptime)}`);
    console.log(`${chalk.cyan('Last Activity:')}   ${chalk.white(lastActivity)}`);
    console.log();

    // Strategy Status
    console.log(chalk.yellow.bold('⚙️  STRATEGY STATUS'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const flashloanStatus = this.stats.flashloanEnabled ? chalk.green('ENABLED') : chalk.red('DISABLED');
    const mevShareStatus = this.stats.mevShareEnabled ? chalk.green('ENABLED') : chalk.red('DISABLED');
    const arbitrageStatus = this.stats.arbitrageEnabled ? chalk.green('ENABLED') : chalk.red('DISABLED');
    
    console.log(`${chalk.cyan('Flashloan Attacks:')} ${flashloanStatus}`);
    console.log(`${chalk.cyan('MEV-Share:')}         ${mevShareStatus}`);
    console.log(`${chalk.cyan('Arbitrage:')}         ${arbitrageStatus}`);
    console.log();

    // Transaction Monitoring
    console.log(chalk.yellow.bold('📡 TRANSACTION MONITORING'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const txRate = this.stats.transactionsPerMinute;
    const txRateColor = txRate > 50 ? chalk.green : txRate > 20 ? chalk.yellow : chalk.red;
    const relevantPercent = this.stats.totalTransactions > 0 
      ? ((this.stats.relevantTransactions / this.stats.totalTransactions) * 100).toFixed(1)
      : '0.0';
    
    console.log(`${chalk.cyan('Total Transactions:')}  ${chalk.white(this.stats.totalTransactions.toLocaleString())}`);
    console.log(`${chalk.cyan('Relevant Transactions:')} ${chalk.white(this.stats.relevantTransactions.toLocaleString())} (${chalk.yellow(relevantPercent)}%)`);
    console.log(`${chalk.cyan('Rate (per minute):')}   ${txRateColor(txRate.toString())}`);
    console.log();

    // MEV Opportunities
    console.log(chalk.yellow.bold('💰 MEV OPPORTUNITIES'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const successRate = this.stats.opportunitiesFound > 0 
      ? ((this.stats.opportunitiesExecuted / this.stats.opportunitiesFound) * 100).toFixed(1)
      : '0.0';
    const totalProfitEth = ethers.formatEther(this.stats.totalProfit);
    const profitColor = parseFloat(totalProfitEth) > 0 ? chalk.green : chalk.gray;
    
    console.log(`${chalk.cyan('Opportunities Found:')} ${chalk.white(this.stats.opportunitiesFound.toString())}`);
    console.log(`${chalk.cyan('Successfully Executed:')} ${chalk.white(this.stats.opportunitiesExecuted.toString())} (${chalk.yellow(successRate)}%)`);
    console.log(`${chalk.cyan('Total Profit:')}        ${profitColor(totalProfitEth)} ETH`);
    console.log();

    // Gas Tracking
    console.log(chalk.yellow.bold('⛽ GAS TRACKING'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const avgGasGwei = this.stats.avgGasPrice > 0 ? ethers.formatUnits(this.stats.avgGasPrice, 'gwei') : '0';
    const maxGasGwei = this.stats.maxGasPrice > 0 ? ethers.formatUnits(this.stats.maxGasPrice, 'gwei') : '0';
    const gasColor = parseFloat(avgGasGwei) > 50 ? chalk.red : parseFloat(avgGasGwei) > 30 ? chalk.yellow : chalk.green;
    
    console.log(`${chalk.cyan('Average Gas Price:')}   ${gasColor(avgGasGwei)} gwei`);
    console.log(`${chalk.cyan('Max Gas Price:')}       ${chalk.white(maxGasGwei)} gwei`);
    console.log();

    // Configuration Display
    console.log(chalk.yellow.bold('⚙️  BOT CONFIGURATION'));
    console.log(chalk.gray('─'.repeat(50)));

    const config = this.stats.configuration;

    // Token Configuration
    console.log(`${chalk.cyan('Primary Token:')}       ${chalk.white(config.primaryToken)}`);
    const targetTokensDisplay = config.targetTokens.length > 0
      ? config.targetTokens.slice(0, 4).join(', ') + (config.targetTokens.length > 4 ? '...' : '')
      : 'None';
    console.log(`${chalk.cyan('Target Tokens:')}       ${chalk.white(targetTokensDisplay)}`);
    console.log(`${chalk.cyan('All Token Pairs:')}     ${config.enableAllTokenPairs ? chalk.green('Enabled') : chalk.red('Disabled')}`);

    // DEX Configuration
    const dexPairsDisplay = config.flashloanDexPairs.length > 0
      ? config.flashloanDexPairs.join(', ')
      : 'None';
    console.log(`${chalk.cyan('DEX Pairs:')}           ${chalk.white(dexPairsDisplay)}`);
    console.log(`${chalk.cyan('Buy DEX:')}             ${chalk.white(config.buyDex)}`);
    console.log(`${chalk.cyan('Sell DEX:')}            ${chalk.white(config.sellDex)}`);
    console.log(`${chalk.cyan('Cross-DEX:')}           ${config.enableCrossDex ? chalk.green('Enabled') : chalk.red('Disabled')}`);

    // Profit & Risk Settings
    const minProfitEth = config.minProfitWei !== '0'
      ? (parseFloat(config.minProfitWei) / 1e18).toFixed(4) + ' ETH'
      : 'Not Set';
    console.log(`${chalk.cyan('Min Profit:')}          ${chalk.white(minProfitEth)}`);
    console.log(`${chalk.cyan('Max Position:')}        ${chalk.white(config.maxPositionSizeEth)} ETH`);
    console.log(`${chalk.cyan('Slippage Tolerance:')}  ${chalk.white(config.slippageTolerance)}%`);

    // Mode & Network
    const modeColor = config.dryRun ? chalk.yellow : chalk.green;
    const modeText = config.dryRun ? 'DRY RUN (Safe)' : 'LIVE TRADING';
    console.log(`${chalk.cyan('Trading Mode:')}        ${modeColor(modeText)}`);
    console.log(`${chalk.cyan('Scan Interval:')}       ${chalk.white(config.arbitrageScanInterval)}`);
    console.log();

    // Error Tracking
    if (this.stats.errors > 0) {
      console.log(chalk.yellow.bold('⚠️  ERROR TRACKING'));
      console.log(chalk.gray('─'.repeat(50)));
      console.log(`${chalk.cyan('Total Errors:')}        ${chalk.red(this.stats.errors.toString())}`);
      if (this.stats.lastError) {
        const truncatedError = this.stats.lastError.length > 60 
          ? this.stats.lastError.substring(0, 60) + '...'
          : this.stats.lastError;
        console.log(`${chalk.cyan('Last Error:')}          ${chalk.red(truncatedError)}`);
      }
      console.log();
    }

    // Successful Transactions Feed
    if (this.successfulTxBuffer.length > 0) {
      console.log(chalk.yellow.bold('🎉 RECENT SUCCESSFUL TRANSACTIONS'));
      console.log(chalk.gray('─'.repeat(80)));

      // Show last 8 transactions
      const recentTxs = this.successfulTxBuffer.slice(-8);
      recentTxs.forEach(tx => {
        const time = new Date(tx.timestamp).toLocaleTimeString();
        const profit = ethers.formatEther(tx.profit);
        const gasUsed = ethers.formatEther(tx.gasUsed);

        // Color code by transaction type
        const typeColor = this.getTypeColor(tx.type);
        const profitColor = parseFloat(profit) > 0.01 ? chalk.green : chalk.yellow;

        // Format transaction line
        let line = `${chalk.gray(time)} ${typeColor(tx.type.toUpperCase().padEnd(10))} `;
        line += `${profitColor('+')}${profitColor(profit)} ETH `;
        line += `${chalk.gray('Gas:')} ${chalk.white(gasUsed)} ETH `;
        line += `${chalk.gray('Conf:')} ${chalk.cyan(tx.confidence + '%')}`;

        // Add hash if available
        if (tx.txHash) {
          const shortHash = tx.txHash.slice(0, 10) + '...';
          line += ` ${chalk.gray('Tx:')} ${chalk.blue(shortHash)}`;
        }

        if (tx.bundleHash) {
          const shortBundle = tx.bundleHash.slice(0, 10) + '...';
          line += ` ${chalk.gray('Bundle:')} ${chalk.magenta(shortBundle)}`;
        }

        // Add details if available
        if (tx.details) {
          line += ` ${chalk.gray('|')} ${chalk.white(tx.details)}`;
        }

        console.log(line);
      });
      console.log();
    }

    // Footer
    console.log(chalk.blue.bold('═══════════════════════════════════════════════════════════════'));
    console.log(chalk.gray(`Last Updated: ${now.toLocaleTimeString()} | Press Ctrl+C to stop`));
    console.log(chalk.blue.bold('═══════════════════════════════════════════════════════════════'));
  }

  /**
   * Get color for transaction type
   */
  private getTypeColor(type: string): (text: string) => string {
    switch (type) {
      case 'flashloan':
        return chalk.green;
      case 'arbitrage':
        return chalk.blue;
      case 'sandwich':
        return chalk.red;
      case 'mev-share':
        return chalk.magenta;
      case 'uniswap-v3-flash':
        return chalk.cyan;
      default:
        return chalk.white;
    }
  }

  /**
   * Format uptime duration
   */
  private formatUptime(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Format time since last activity
   */
  private formatTimeSince(timestamp: number): string {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    
    if (seconds < 60) {
      return `${seconds}s ago`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ago`;
    } else {
      return `${Math.floor(seconds / 3600)}h ago`;
    }
  }

  /**
   * Get current stats
   */
  getStats(): DashboardStats {
    return { ...this.stats };
  }
}

// Global dashboard instance
export const statusDashboard = new StatusDashboard();
